import asyncio
import os
import queue
import threading
from typing import AsyncGenerator, Dict, List, Optional

import streamlit as st
from crewai import Agent, Crew, Task
from crewai_tools import ScrapeWebsiteTool

from agent_learn.llm import QwenLLM


class SupportMessageQueue:
    def __init__(self):
        self.message_queue = queue.Queue()
        self.last_agent = None

    def add_message(self, message: Dict):
        self.message_queue.put(message)

    def get_messages(self) -> List[Dict]:
        messages = []
        while not self.message_queue.empty():
            messages.append(self.message_queue.get())
        return messages


class SupportCrew:
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.message_queue = SupportMessageQueue()
        self.support_agent = None
        self.qa_agent = None
        self.current_agent = None
        self.scrape_tool = None
        self.llm = QwenLLM(api_key=self.api_key, model=self.model, endpoint=self.base_url)

    def initialize_agents(self, website_url: str):
        if not self.api_key:
            raise ValueError("qwen API key is required")

        os.environ["qwen_API_KEY"] = self.api_key
        self.scrape_tool = ScrapeWebsiteTool(website_url=website_url)

        self.support_agent = Agent(
            role="Senior Support Representative",
            goal="Be the most friendly and helpful support representative in your team",
            backstory=(
                "You work at crewAI and are now working on providing support to customers. "
                "You need to make sure that you provide the best support! "
                "Make sure to provide full complete answers, and make no assumptions."
            ),
            allow_delegation=False,
            verbose=True,
            llm=self.llm,
        )

        self.qa_agent = Agent(
            role="Support Quality Assurance Specialist",
            goal="Get recognition for providing the best support quality assurance in your team",
            backstory=(
                "You work at crewAI and are now working with your team on customer requests "
                "ensuring that the support representative is providing the best support possible. "
                "You need to make sure that the support representative is providing full "
                "complete answers, and make no assumptions."
            ),
            verbose=True,
            llm=self.llm,
        )

    # Implement Task Creation for the agents
    def create_tasks(self, inquiry: str) -> List[Task]:
        inquiry_resolution = Task(
            description=(
                f"A customer just reached out with a super important ask:\n{inquiry}\n\n"
                "Make sure to use everything you know to provide the best support possible. "
                "You must strive to provide a complete and accurate response to the customer's inquiry."
            ),
            expected_output=(
                "A detailed, informative response to the customer's inquiry that addresses "
                "all aspects of their question.\n"
                "The response should include references to everything you used to find the answer."
            ),
            tools=[self.scrape_tool],
            agent=self.support_agent,
        )

        quality_assurance_review = Task(
            description=(
                "Review the response drafted by the Senior Support Representative for the customer's inquiry. "
                "Ensure that the answer is comprehensive, accurate, and adheres to the "
                "high-quality standards expected for customer support."
            ),
            expected_output=(
                "A final, detailed, and informative response ready to be sent to the customer.\n"
                "This response should fully address the customer's inquiry, incorporating all "
                "relevant feedback and improvements."
            ),
            agent=self.qa_agent,
        )

        return [inquiry_resolution, quality_assurance_review]

    # main processing function
    async def process_support(self, inquiry: str, website_url: str) -> AsyncGenerator[List[Dict], None]:
        print(f"Processing support for inquiry: {inquiry}")

        def add_agent_messages(agent_name: str, tasks: str, emoji: str = "🤖"):
            self.message_queue.add_message({"role": "assistant", "content": agent_name, "metadata": {"title": f"{emoji} {agent_name}"}})

            self.message_queue.add_message({"role": "assistant", "content": tasks, "metadata": {"title": f"📋 Task for {agent_name}"}})

        # Manages transition between agents
        def setup_next_agent(current_agent: str) -> None:
            if current_agent == "Senior Support Representative":
                self.current_agent = "Support Quality Assurance Specialist"
                add_agent_messages("Support Quality Assurance Specialist", "Review and improve the support representative's response")

        def task_callback(task_output) -> None:
            print(f"Task callback received: {task_output}")

            raw_output = task_output.raw
            if "## Final Answer:" in raw_output:
                content = raw_output.split("## Final Answer:")[1].strip()
            else:
                content = raw_output.strip()

            if self.current_agent == "Support Quality Assurance Specialist":
                self.message_queue.add_message(
                    {"role": "assistant", "content": "Final response is ready!", "metadata": {"title": "✅ Final Response"}}
                )

                formatted_content = content
                formatted_content = formatted_content.replace("\n#", "\n\n#")
                formatted_content = formatted_content.replace("\n-", "\n\n-")
                formatted_content = formatted_content.replace("\n*", "\n\n*")
                formatted_content = formatted_content.replace("\n1.", "\n\n1.")
                formatted_content = formatted_content.replace("\n\n\n", "\n\n")

                self.message_queue.add_message({"role": "assistant", "content": formatted_content})
            else:
                self.message_queue.add_message(
                    {"role": "assistant", "content": content, "metadata": {"title": f"✨ Output from {self.current_agent}"}}
                )
                setup_next_agent(self.current_agent)

        try:
            self.initialize_agents(website_url)
            self.current_agent = "Senior Support Representative"

            print("------------------------------")
            yield [{"role": "assistant", "content": "Starting to process your inquiry...", "metadata": {"title": "🚀 Process Started"}}]

            add_agent_messages("Senior Support Representative", "Analyze customer inquiry and provide comprehensive support")

            crew = Crew(agents=[self.support_agent, self.qa_agent], tasks=self.create_tasks(inquiry), verbose=True, task_callback=task_callback)

            def run_crew():
                try:
                    crew.kickoff()
                except Exception as e:
                    print(f"Error in crew execution: {str(e)}")
                    self.message_queue.add_message(
                        {"role": "assistant", "content": f"An error occurred: {str(e)}", "metadata": {"title": "❌ Error"}}
                    )

            thread = threading.Thread(target=run_crew)
            thread.start()

            while thread.is_alive() or not self.message_queue.message_queue.empty():
                messages = self.message_queue.get_messages()
                if messages:
                    print(f"Yielding messages: {messages}")
                    yield messages
                await asyncio.sleep(0.1)

        except Exception as e:
            print(f"Error in process_support: {str(e)}")
            yield [{"role": "assistant", "content": f"An error occurred: {str(e)}", "metadata": {"title": "❌ Error"}}]


def main():
    st.set_page_config(page_title="AI Customer Support Crew", page_icon="🎯", layout="wide")

    st.title("🎯 AI Customer Support Crew")
    st.markdown(
        "This is a friendly, high-performing multi-agent application built with Streamlit and CrewAI. Enter a webpage URL and your questions from that webpage."
    )

    st.session_state.confirm = False
    st.session_state.button_clicked = False
    # Initialize session state
    if "support_crew" not in st.session_state:
        st.session_state.support_crew = None
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "api_key_entered" not in st.session_state:
        st.session_state.api_key_entered = False
    if "confirm" not in st.session_state:
        st.session_state.confirm = False
    if "button_clicked" not in st.session_state:
        st.session_state.button_clicked = False

    with st.sidebar:
        qwen_api_key = st.text_input(
            "Qwen API Key",
            type="password",
            placeholder="Type your qwen API Key and press Enter to access the app...",
            key="api_key_input",
            value="sk-80f0c11034f84c8b9d95fa686a73a08b",
        )
        qwen_base_url = st.text_input(
            "Qwen Base URL",
            type="default",
            value="https://dashscope.aliyuncs.com/compatible-mode",
            placeholder="Type your qwen Base URL and press Enter to access the app...",
            key="base_url_input",
        )
        qwen_model = st.selectbox(
            "Qwen Models",
            ["qwen-max-2025-01-25", "qwen-turbo-2025-04-28", "qwen-turbo-0919", "qwen-plus-2025-01-25", "qwen-plus-2025-04-28", "qwq-plus"],
            index=0,
            key="models_input",
        )

        if st.button("Confirm Information"):
            if qwen_api_key and qwen_base_url and qwen_model:
                st.session_state.qwen_api_key = qwen_api_key
                st.session_state.qwen_base_url = qwen_base_url
                st.session_state.qwen_model = qwen_model
                st.session_state.api_key_entered = True
                st.session_state.support_crew = SupportCrew(api_key=qwen_api_key, base_url=qwen_base_url, model=qwen_model)
                st.session_state.confirm = True
            else:
                st.error("Please provide an qwen API key or base url or model.")

    if st.session_state.button_clicked and st.session_state.get("auto_submit", False):
        # Display chat messages
        with st.container(border=True):
            if st.session_state.messages:
                st.subheader("Support Process")

                for message in st.session_state.messages:
                    if message["role"] == "user":
                        with st.chat_message("user"):
                            st.write(message["content"])
                    else:
                        with st.chat_message("assistant"):
                            # Display title if available
                            if "metadata" in message and "title" in message["metadata"]:
                                st.markdown(f"**{message['metadata']['title']}**")
                            st.write(message["content"])

    with st.container(border=True):
        col1, col2 = st.columns(2)

        with col1:
            inquiry = st.text_input("Your Inquiry", placeholder="Enter your question...", key="inquiry_input")

        with col2:
            website_url = st.text_input("Documentation URL", placeholder="Enter documentation URL to search...", key="website_url_input")

        # Submit button logic
        if st.session_state.button_clicked or (inquiry and website_url and st.session_state.get("auto_submit", False)):
            if inquiry and website_url:
                # Add user message to chat
                user_message = f"Question: {inquiry}\nDocumentation: {website_url}"
                st.markdown(f"**Question:** {inquiry}\n**Documentation:** {website_url}")
                st.session_state.messages.append({"role": "user", "content": user_message})

                # Process the support request
                with st.spinner("Processing your inquiry..."):
                    try:
                        # Create a placeholder for streaming messages
                        message_placeholder = st.empty()

                        # Process support request
                        async def process_request():
                            async for messages in st.session_state.support_crew.process_support(inquiry, website_url):
                                st.session_state.messages.extend(messages)

                        # Run the async function
                        import asyncio

                        asyncio.run(process_request())

                    except Exception as e:
                        st.session_state.messages.append(
                            {"role": "assistant", "content": f"An error occurred: {str(e)}", "metadata": {"title": "❌ Error"}}
                        )
            else:
                st.error("Please enter both your inquiry and documentation URL.")

    with st.container():
        col1, col2, _ = st.columns([1, 1, 8])

        with col1:
            if st.button("Get Support", type="primary"):
                st.session_state.button_clicked = True
                st.session_state.auto_submit = True

        with col2:
            # Clear chat button
            if st.button("Clear Chat"):
                st.session_state.messages = []
                st.rerun()


if __name__ == "__main__":
    main()
