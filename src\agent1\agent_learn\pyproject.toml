[project]
name = "agent_learn"
version = "0.1.0"
description = "agent_learn using crewA<PERSON>"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.121.1,<1.0.0",
    "streamlit>=1.45.1",
]

[project.scripts]
agent_learn = "agent_learn.main:run"
run_crew = "agent_learn.main:run"
train = "agent_learn.main:train"
replay = "agent_learn.main:replay"
test = "agent_learn.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"

[tool.mypy]
ignore_missing_imports = true
disable_error_code = ["import-untyped", "arg-type", "attr-defined", "type-arg", "valid-type", "union-attr", "call-arg", "annotation-unchecked", "misc"]

[[tool.mypy.overrides]]
module = "*"
disable_error_code = ["import-untyped", "arg-type", "attr-defined", "type-arg", "valid-type", "union-attr", "call-arg", "annotation-unchecked", "misc"]
ignore_missing_imports = true

# https://github.com/microsoft/pyright/blob/main/docs/configuration.md
[tool.pyright]
include = ["src"]
exclude = ["**/__pycache__", ".venv"]
reportMissingImports = true
reportCallIssue = false
reportArgumentType = false
reportGeneralTypeIssues = false
reportPossiblyUnboundVariable = false
reportOptionalMemberAccess = false
