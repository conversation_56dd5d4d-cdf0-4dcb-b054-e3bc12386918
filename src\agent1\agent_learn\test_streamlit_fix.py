#!/usr/bin/env python3
"""
Test script to verify the Streamlit app fixes
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all imports work correctly"""
    try:
        from agent_learn.page.streamlit_app import SupportCrew, SupportMessageQueue, main
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_support_message_queue():
    """Test the SupportMessageQueue functionality"""
    try:
        queue = SupportMessageQueue()
        
        # Test adding messages
        queue.add_message({"role": "assistant", "content": "Test message"})
        
        # Test getting messages
        messages = queue.get_messages()
        assert len(messages) == 1
        assert messages[0]["content"] == "Test message"
        
        # Test completion status
        assert not queue.is_complete()
        queue.mark_complete()
        assert queue.is_complete()
        
        print("✅ SupportMessageQueue tests passed")
        return True
    except Exception as e:
        print(f"❌ SupportMessageQueue test failed: {e}")
        return False

def test_support_crew_initialization():
    """Test SupportCrew initialization"""
    try:
        crew = SupportCrew(
            api_key="test-key",
            base_url="https://test.com",
            model="test-model"
        )
        
        assert crew.api_key == "test-key"
        assert crew.base_url == "https://test.com"
        assert crew.model == "test-model"
        assert crew.message_queue is not None
        
        print("✅ SupportCrew initialization test passed")
        return True
    except Exception as e:
        print(f"❌ SupportCrew initialization test failed: {e}")
        return False

def main_test():
    """Run all tests"""
    print("🧪 Testing Streamlit app fixes...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_support_message_queue,
        test_support_crew_initialization,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes look good.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main_test()
    sys.exit(0 if success else 1)
