import asyncio
import queue
import threading
from typing import AsyncGenerator, Dict, List, Optional

from crewai import Agent, Crew, Task
from crewai_tools import ScrapeWebsiteTool

from agent_learn.llm import QwenLLM


class SupportMessageQueue:
    def __init__(self):
        self.message_queue = queue.Queue()
        self.last_agent = None

    def add_message(self, message: Dict):
        self.message_queue.put(message)

    def get_messages(self) -> List[Dict]:
        messages = []
        while not self.message_queue.empty():
            messages.append(self.message_queue.get())
        return messages


class SupportCrew:
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None, model: Optional[str] = None):
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.message_queue = SupportMessageQueue()
        self.support_agent = None
        self.qa_agent = None
        self.current_agent = None
        self.scrape_tool = None
        self.llm = QwenLLM(api_key=self.api_key, model=self.model, endpoint=self.base_url)

    def initialize_agents(self, website_url: str):
        if not self.api_key:
            raise ValueError("qwen API key is required")

        self.scrape_tool = ScrapeWebsiteTool(website_url=website_url)

        self.support_agent = Agent(
            role="Senior Support Representative",
            goal="Be the most friendly and helpful support representative in your team",
            backstory=(
                "You work at crewAI and are now working on providing support to customers. "
                "You need to make sure that you provide the best support! "
                "Make sure to provide full complete answers, and make no assumptions."
            ),
            allow_delegation=False,
            verbose=True,
            llm=self.llm,
        )

        self.qa_agent = Agent(
            role="Support Quality Assurance Specialist",
            goal="Get recognition for providing the best support quality assurance in your team",
            backstory=(
                "You work at crewAI and are now working with your team on customer requests "
                "ensuring that the support representative is providing the best support possible. "
                "You need to make sure that the support representative is providing full "
                "complete answers, and make no assumptions."
            ),
            verbose=True,
            llm=self.llm,
        )

    # Implement Task Creation for the agents
    def create_tasks(self, inquiry: str) -> List[Task]:
        inquiry_resolution = Task(
            description=(
                f"A customer just reached out with a super important ask:\n{inquiry}\n\n"
                "Make sure to use everything you know to provide the best support possible. "
                "You must strive to provide a complete and accurate response to the customer's inquiry."
            ),
            expected_output=(
                "A detailed, informative response to the customer's inquiry that addresses "
                "all aspects of their question.\n"
                "The response should include references to everything you used to find the answer."
            ),
            # tools=[self.scrape_tool],
            agent=self.support_agent,
        )

        quality_assurance_review = Task(
            description=(
                "Review the response drafted by the Senior Support Representative for the customer's inquiry. "
                "Ensure that the answer is comprehensive, accurate, and adheres to the "
                "high-quality standards expected for customer support."
            ),
            expected_output=(
                "A final, detailed, and informative response ready to be sent to the customer.\n"
                "This response should fully address the customer's inquiry, incorporating all "
                "relevant feedback and improvements."
            ),
            agent=self.qa_agent,
        )

        return [inquiry_resolution, quality_assurance_review]

    # main processing function
    async def process_support(self, inquiry: str, website_url: str) -> AsyncGenerator[List[Dict], None]:
        print(f"Processing support for inquiry: {inquiry}")

        def add_agent_messages(agent_name: str, tasks: str, emoji: str = "🤖"):
            self.message_queue.add_message({"role": "assistant", "content": agent_name, "metadata": {"title": f"{emoji} {agent_name}"}})

            self.message_queue.add_message({"role": "assistant", "content": tasks, "metadata": {"title": f"📋 Task for {agent_name}"}})

        # Manages transition between agents
        def setup_next_agent(current_agent: str) -> None:
            if current_agent == "Senior Support Representative":
                self.current_agent = "Support Quality Assurance Specialist"
                add_agent_messages("Support Quality Assurance Specialist", "Review and improve the support representative's response")

        def task_callback(task_output) -> None:
            print(f"Task callback received: {task_output}")

            raw_output = task_output.raw
            if "## Final Answer:" in raw_output:
                content = raw_output.split("## Final Answer:")[1].strip()
            else:
                content = raw_output.strip()

            if self.current_agent == "Support Quality Assurance Specialist":
                self.message_queue.add_message(
                    {"role": "assistant", "content": "Final response is ready!", "metadata": {"title": "✅ Final Response"}}
                )

                formatted_content = content
                formatted_content = formatted_content.replace("\n#", "\n\n#")
                formatted_content = formatted_content.replace("\n-", "\n\n-")
                formatted_content = formatted_content.replace("\n*", "\n\n*")
                formatted_content = formatted_content.replace("\n1.", "\n\n1.")
                formatted_content = formatted_content.replace("\n\n\n", "\n\n")

                self.message_queue.add_message({"role": "assistant", "content": formatted_content})
            else:
                self.message_queue.add_message(
                    {"role": "assistant", "content": content, "metadata": {"title": f"✨ Output from {self.current_agent}"}}
                )
                setup_next_agent(self.current_agent)

        try:
            self.initialize_agents(website_url)
            self.current_agent = "Senior Support Representative"

            print("------------------------------")
            yield [{"role": "assistant", "content": "Starting to process your inquiry...", "metadata": {"title": "🚀 Process Started"}}]

            add_agent_messages("Senior Support Representative", "Analyze customer inquiry and provide comprehensive support")

            crew = Crew(agents=[self.support_agent, self.qa_agent], tasks=self.create_tasks(inquiry), verbose=True, task_callback=task_callback)

            def run_crew():
                try:
                    crew.kickoff()
                except Exception as e:
                    print(f"Error in crew execution: {str(e)}")
                    self.message_queue.add_message(
                        {"role": "assistant", "content": f"An error occurred: {str(e)}", "metadata": {"title": "❌ Error"}}
                    )

            thread = threading.Thread(target=run_crew)
            thread.start()

            while thread.is_alive() or not self.message_queue.message_queue.empty():
                messages = self.message_queue.get_messages()
                if messages:
                    print(f"Yielding messages: {messages}")
                    yield messages
                await asyncio.sleep(0.1)

        except Exception as e:
            print(f"Error in process_support: {str(e)}")
            yield [{"role": "assistant", "content": f"An error occurred: {str(e)}", "metadata": {"title": "❌ Error"}}]


if __name__ == "__main__":
    support_crew = SupportCrew(
        api_key="sk-80f0c11034f84c8b9d95fa686a73a08b",
        base_url="https://dashscope.aliyuncs.com/compatible-mode",
        model="qwen3-235b-a22b",
    )
    inquiry = "how to build your first crew"
    website_url = "https://docs.crewai.com/guides/crews/first-crew"

    async def main():
        async for messages in support_crew.process_support(inquiry, website_url):
            print(messages)

    asyncio.run(main())
